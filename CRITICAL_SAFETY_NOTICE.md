# 🚨 CRITICAL SAFETY NOTICE - Zotero Script Usage

## ⚠️ **IMMEDIATE ACTION REQUIRED**

If you are experiencing Zotero stability issues, extension corruption, or performance problems after using the ZoteroPlugin scripts, please follow these steps immediately:

### **🛑 STOP Using Actions & Tags Extension for Large Scripts**

**Problem Identified**: The "Actions and Tags for Zotero" extension is **NOT SAFE** for large scripts like ZoteroPlugin_v2.2_Enhanced.js (1,554 lines, ~58KB). Using it causes:
- ❌ **Preference system corruption** (58KB written to preferences)
- ❌ **Extension UI breakdown** (`addon.data.prefs.tableHelper.treeInstance is undefined`)
- ❌ **Severe performance degradation**
- ❌ **Potential data loss**

### **🔧 IMMEDIATE RECOVERY STEPS**

#### **Step 1: Disable Actions & Tags Extension**
1. Open Zotero
2. Go to **Tools → Add-ons**
3. Find "Actions and Tags for Zotero"
4. Click **Disable**
5. Restart Zotero

#### **Step 2: Remove Problematic Rules**
1. Re-enable the extension temporarily
2. Go to **Tools → Actions and Tags → Settings**
3. **DELETE** any rules containing large scripts (>1KB)
4. Look for rules named "AutoTagger", "Abstract Extractor", or similar
5. **Save** settings
6. Disable extension again if needed

#### **Step 3: Clear Corrupted Preferences (If Needed)**
If Zotero is still unstable:
1. Close Zotero completely
2. Navigate to your Zotero profile folder
3. **Backup** your `prefs.js` file
4. Consider resetting Actions & Tags preferences

### **✅ SAFE USAGE METHODS**

#### **Method 1: Zotero Built-in JavaScript Runner (RECOMMENDED)**
```
1. Open Zotero
2. Go to Tools → Developer → Run JavaScript
3. Select items in your library FIRST
4. Paste the script code
5. Click "Run"
```

**Benefits:**
- ✅ No size limitations
- ✅ No preference corruption
- ✅ Full feature access
- ✅ Safe for all script versions

#### **Method 2: Actions & Tags (ONLY for Small Scripts)**
**ONLY use Actions & Tags for scripts under 500 characters (~10 lines)**

**Safe Example:**
```javascript
// SAFE - Small script for Actions & Tags
if (!items.length) return "No items selected";
let count = 0;
items.forEach(item => {
    if (item.isRegularItem()) count++;
});
return `Found ${count} regular items`;
```

### **📏 SCRIPT SIZE GUIDELINES**

| Script Size | Actions & Tags | Built-in Runner | Recommendation |
|-------------|----------------|-----------------|----------------|
| **< 500 chars** | ✅ Safe | ✅ Safe | Either method |
| **500-2000 chars** | ⚠️ Risky | ✅ Safe | Use Built-in Runner |
| **> 2000 chars** | ❌ Dangerous | ✅ Safe | **ONLY Built-in Runner** |

### **🔍 HOW TO CHECK SCRIPT SIZE**

#### **Character Count Check:**
```javascript
// Paste your script here and count characters
const scriptContent = `YOUR_SCRIPT_HERE`;
console.log(`Script size: ${scriptContent.length} characters`);
```

#### **File Size Check:**
- **ZoteroPlugin.js**: ~18KB (❌ Too large for Actions & Tags)
- **ZoteroPlugin_v2.2_Enhanced.js**: ~58KB (❌ WAY too large for Actions & Tags)

### **🚨 WARNING SIGNS OF CORRUPTION**

If you experience any of these, you may have corrupted preferences:

#### **Zotero Symptoms:**
- Slow startup or operation
- Frequent crashes or freezes
- Error messages about preferences
- Extensions not loading properly

#### **Actions & Tags Symptoms:**
- Extension interface not loading
- "treeInstance is undefined" errors
- Settings page blank or broken
- Rules not executing

#### **Debug Log Warnings:**
- "attempting to write XXXXX bytes to preference"
- "This is bad for general performance and memory usage"
- Extension-related JavaScript errors

### **🛠️ RECOVERY PROCEDURES**

#### **Level 1: Soft Reset**
1. Disable Actions & Tags extension
2. Remove large script rules
3. Restart Zotero
4. Test functionality

#### **Level 2: Preference Cleanup**
1. Close Zotero
2. Backup Zotero profile
3. Reset Actions & Tags preferences
4. Restart and reconfigure

#### **Level 3: Full Reset (Last Resort)**
1. Export your library (File → Export Library)
2. Create new Zotero profile
3. Import library
4. Reinstall extensions carefully

### **📋 PREVENTION CHECKLIST**

Before using ANY script with Actions & Tags:

- [ ] **Check script size** (must be < 500 characters)
- [ ] **Test with small item set** first
- [ ] **Monitor Zotero performance** after installation
- [ ] **Backup your library** before major script installations
- [ ] **Use Built-in Runner** for complex scripts

### **🎯 RECOMMENDED WORKFLOW**

#### **For ZoteroPlugin Scripts:**
1. **Select items** in Zotero library
2. **Open Built-in Runner**: Tools → Developer → Run JavaScript
3. **Paste script** (any version: original, v2.2, etc.)
4. **Click Run**
5. **Check results** in debug log

#### **For Simple Automation:**
1. **Write short scripts** (< 500 chars) for Actions & Tags
2. **Use Built-in Runner** for anything complex
3. **Test thoroughly** before deploying

### **📞 GETTING HELP**

#### **If You're Still Having Issues:**
1. **Check Zotero debug log**: Help → Debug Output Logging
2. **Look for preference warnings** or extension errors
3. **Try safe mode**: Start Zotero with extensions disabled
4. **Report to Actions & Tags GitHub** if extension-related

#### **Bug Report Template:**
```
Zotero Version: [version]
Actions & Tags Version: [version]
Script Size: [character count]
Error Message: [exact error]
Steps to Reproduce: [detailed steps]
```

### **🔮 FUTURE SAFETY**

#### **Actions & Tags Extension Should:**
- ✅ **Validate script size** before saving
- ✅ **Warn users** about large scripts
- ✅ **Use alternative storage** for large scripts
- ✅ **Provide size limits** in UI

#### **Users Should:**
- ✅ **Always check script size** before using Actions & Tags
- ✅ **Use Built-in Runner** for complex scripts
- ✅ **Monitor system performance** after script installation
- ✅ **Keep backups** of library and preferences

---

## 🎯 **SUMMARY**

**✅ SAFE**: Use Zotero's Built-in JavaScript Runner (Tools → Developer → Run JavaScript) for ALL ZoteroPlugin scripts

**❌ UNSAFE**: Using Actions & Tags extension for large scripts (>500 characters) causes system corruption

**🔧 RECOVERY**: Disable Actions & Tags, remove large script rules, restart Zotero

**📏 RULE**: Actions & Tags only for tiny scripts (<500 chars), Built-in Runner for everything else

This safety notice will be updated as we learn more about the interaction between large scripts and the Actions & Tags extension.
