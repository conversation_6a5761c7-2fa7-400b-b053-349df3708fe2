# Zotero API Troubleshooting Guide

## 🚨 **Common Error: "Components.classes is undefined"**

### **Error Message**
```
Script error: Failed to initialize Zotero API, Components.classes['@zotero.org/Zotero;1'] is undefined
```

### **Root Cause**
This error occurs when the script tries to use the legacy XPCOM approach to access Zotero, but the `Components` object is not available in the current execution context. This typically happens in:
- Modern Zotero versions (6+) where the API has changed
- Different script execution contexts
- Browser-based environments where XPCOM is not available

### **✅ Solution Applied**
The scripts have been updated with improved Zotero API initialization that tries multiple approaches:

```javascript
// Modern Zotero API initialization (Zotero 6+)
if (typeof window !== 'undefined' && window.Zotero) {
    Zotero = window.Zotero;
}
// Global Zotero object (common in script contexts)
else if (typeof globalThis !== 'undefined' && globalThis.Zotero) {
    Zotero = globalThis.Zotero;
}
// Check if Zotero is available as a global variable
else if (typeof Zotero !== 'undefined') {
    // Zotero is already available globally
    // This is the most common case for Zotero scripts
}
// Legacy XPCOM approach (Zotero 5 and earlier) - only if Components is available
else if (typeof Components !== 'undefined' && Components.classes) {
    try {
        Zotero = Components.classes['@zotero.org/Zotero;1']
            .getService(Components.interfaces.nsISupports).wrappedJSObject;
    } catch (error) {
        throw new Error(`Legacy Zotero API initialization failed: ${error.message}`);
    }
}
else {
    throw new Error("Could not find Zotero API. Please ensure this script is run within Zotero.");
}
```

## 🔧 **How to Run Zotero Scripts Properly**

### **Method 1: Zotero Actions & Tags Plugin (Recommended)**
1. **Install the plugin**: [Zotero Actions & Tags](https://github.com/windingwind/zotero-actions-tags)
2. **Add the script**: Copy the script content into the plugin
3. **Select items**: Select items in your Zotero library
4. **Run the script**: Execute through the plugin interface

### **Method 2: Zotero Debug Console**
1. **Open Debug Console**: Help → Debug Output Logging → View Output
2. **Enable logging**: Check "Enable" if not already enabled
3. **Run script**: Paste the script in the console and execute
4. **Note**: Some features may not work in debug console

### **Method 3: Zotero Plugin Development**
1. **Create a plugin**: For advanced users who want to create a proper Zotero plugin
2. **Use plugin template**: Follow Zotero plugin development guidelines
3. **Package and install**: Create a proper .xpi file

## 🔍 **Diagnosing API Issues**

### **Check Zotero Version**
```javascript
// Add this to the beginning of your script to check Zotero version
if (typeof Zotero !== 'undefined') {
    console.log("Zotero version:", Zotero.version);
    console.log("Zotero platform:", Zotero.platform);
} else {
    console.log("Zotero object not available");
}
```

### **Check Available APIs**
```javascript
// Check what's available in your environment
console.log("window.Zotero:", typeof window !== 'undefined' ? !!window.Zotero : 'window not available');
console.log("globalThis.Zotero:", typeof globalThis !== 'undefined' ? !!globalThis.Zotero : 'globalThis not available');
console.log("Global Zotero:", typeof Zotero !== 'undefined');
console.log("Components:", typeof Components !== 'undefined');
console.log("Services:", typeof Services !== 'undefined');
```

## 🛠️ **Alternative Solutions**

### **If Scripts Still Don't Work**

#### **Option 1: Use Zotero Actions & Tags Plugin**
This is the most reliable method for running custom scripts in Zotero:
1. Install the plugin from GitHub
2. The plugin provides a proper execution environment
3. All API features are available
4. Best compatibility across Zotero versions

#### **Option 2: Simplify the Script**
If you're having persistent issues, try a minimal version:
```javascript
// Minimal script version - just check if basic functionality works
if (typeof Zotero === 'undefined') {
    return "Zotero API not available";
}

if (typeof items === 'undefined' || !items.length) {
    return "No items selected";
}

return `Found ${items.length} items. Zotero version: ${Zotero.version}`;
```

#### **Option 3: Check Execution Context**
```javascript
// Debug script to understand your environment
const debugInfo = {
    hasWindow: typeof window !== 'undefined',
    hasGlobalThis: typeof globalThis !== 'undefined',
    hasZotero: typeof Zotero !== 'undefined',
    hasComponents: typeof Components !== 'undefined',
    hasServices: typeof Services !== 'undefined',
    hasItems: typeof items !== 'undefined',
    itemsLength: typeof items !== 'undefined' ? items.length : 'N/A'
};

console.log("Debug Info:", JSON.stringify(debugInfo, null, 2));
return JSON.stringify(debugInfo, null, 2);
```

## 📋 **Compatibility Matrix**

| Zotero Version | Recommended Method | API Access | Components Available |
|----------------|-------------------|------------|---------------------|
| **Zotero 6+** | Actions & Tags Plugin | `window.Zotero` or global `Zotero` | Usually No |
| **Zotero 5** | Actions & Tags Plugin | `Components.classes` | Yes |
| **Debug Console** | Limited functionality | Varies | Varies |

## 🔄 **Updated Files**

The following files have been updated to fix the API initialization issue:
- ✅ `ZoteroPlugin.js` (original version)
- ✅ `ZoteroPlugin_v2.2_Enhanced.js` (latest version)

Both files now include:
- **Multi-method API detection**: Tries modern approaches first, falls back to legacy
- **Better error messages**: More specific error information
- **Safer initialization**: Checks for availability before attempting access

## 📞 **Getting Help**

If you're still experiencing issues:

1. **Check Zotero version**: Ensure you're using a supported version
2. **Try the debug script**: Run the minimal debug script above
3. **Use Actions & Tags plugin**: This is the most reliable method
4. **Check the debug console**: Look for additional error messages
5. **Report the issue**: Include the debug information and your Zotero version

## ✅ **Verification Steps**

After applying the fixes:
1. **Test with minimal script**: Try the debug script first
2. **Check error messages**: Look for more specific error information
3. **Verify item selection**: Ensure items are selected before running
4. **Check debug output**: Enable debug logging to see detailed information

The updated scripts should now work across different Zotero versions and execution contexts.
