/**
 * Compact Abstract and Keyword Extractor for Zotero
 * SAFE FOR ACTIONS & TAGS EXTENSION
 * 
 * This is a streamlined version designed specifically for the Actions & Tags extension.
 * For the full v2.2 Enhanced version with all features, use Zotero's built-in JavaScript runner.
 * 
 * @version 2.2-Compact
 * @size ~8KB (safe for Actions & Tags)
 */

// --- COMPACT CONFIGURATION ---
const config = {
    overwriteAbstract: true,
    tagPrefix: "",
    abstractLabels: ["Abstract", "Summary", "Zusammenfassung", "Résumé"],
    keywordLabels: ["Keywords", "Key words", "Index Terms", "Schlüsselwörter"],
    maxConcurrentItems: 3,
    pdfExtractionTimeout: 30000
};

// --- SAFE ZOTERO API INITIALIZATION ---
let Zotero;
if (typeof window !== 'undefined' && window.Zotero) {
    Zotero = window.Zotero;
} else if (typeof globalThis !== 'undefined' && globalThis.Zotero) {
    Zotero = globalThis.Zotero;
} else if (typeof Zotero === 'undefined') {
    return "Zotero API not available. Please run this script in Zotero.";
}

// --- LOGGING ---
function log(message, level = 'info') {
    const prefix = `[Compact Extractor]`;
    if (level === 'error') {
        Zotero.logError(`${prefix} ERROR: ${message}`);
    } else {
        Zotero.debug(`${prefix} ${message}`);
    }
}

// --- DOCUMENT TYPE DETECTION (Simplified) ---
function detectDocumentType(text) {
    if (/\babstract\b.*?\bintroduction\b/is.test(text) || /\bdoi:\s*10\./i.test(text)) {
        return 'academic';
    }
    if (/\bproceedings\b/i.test(text) || /\bconference\b/i.test(text)) {
        return 'conference';
    }
    if (/\btechnical\s+report\b/i.test(text) || /\bwhite\s+paper\b/i.test(text)) {
        return 'technical';
    }
    return 'generic';
}

// --- EXTRACTION FUNCTIONS ---
function createRegexPatterns(labels) {
    const escapedLabels = labels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
    return new RegExp(`\\n\\s*(?:${escapedLabels})\\s*[:\\-\\s\\n]([^\\n]{1,50}[\\s\\S]{0,3000}?)(?=\\n\\s*[A-Z])`, 'i');
}

function extractAbstract(text, documentType) {
    let labels = config.abstractLabels;
    if (documentType === 'technical') {
        labels = [...labels, 'Executive Summary', 'Overview'];
    }
    
    const pattern = createRegexPatterns(labels);
    const match = text.match(pattern);
    
    if (match && match[1]) {
        return match[1]
            .replace(/-\n\s*/g, '')
            .replace(/\s+/g, ' ')
            .trim()
            .substring(0, 10000);
    }
    return "";
}

function extractKeywords(text, documentType) {
    let labels = config.keywordLabels;
    if (documentType === 'conference') {
        labels = [...labels, 'Categories', 'Subject Terms'];
    }
    
    const pattern = createRegexPatterns(labels);
    const match = text.match(pattern);
    
    if (match && match[1]) {
        return match[1]
            .trim()
            .split(/[;,.]\s*/)
            .map(k => k.trim().toLowerCase())
            .filter(k => k && k.length >= 2 && k.length <= 100)
            .filter((k, idx, self) => self.indexOf(k) === idx)
            .slice(0, 20);
    }
    return [];
}

// --- PDF TEXT EXTRACTION ---
async function extractPdfText(attachmentId) {
    try {
        const result = await Zotero.PDFWorker.getFullText(attachmentId, 2);
        if (result && result.text) {
            return result.text.length > 500000 ? result.text.substring(0, 500000) : result.text;
        }
    } catch (error) {
        log(`PDF extraction failed for ${attachmentId}: ${error.message}`, 'error');
    }
    return "";
}

// --- MAIN PROCESSING ---
async function processItem(item, index, total) {
    const prefix = `[${index + 1}/${total}]`;
    const title = item.getDisplayTitle() || `Item ${item.id}`;
    
    try {
        if (!item.isRegularItem()) {
            return { success: false, reason: "Not a regular item" };
        }
        
        const attachment = await item.getBestAttachment();
        if (!attachment || (attachment.attachmentContentType || "") !== 'application/pdf') {
            return { success: false, reason: "No PDF attachment" };
        }
        
        const pdfText = await extractPdfText(attachment.id);
        if (!pdfText) {
            return { success: false, reason: "PDF text extraction failed" };
        }
        
        const documentType = detectDocumentType(pdfText);
        const abstract = extractAbstract(pdfText, documentType);
        const keywords = extractKeywords(pdfText, documentType);
        
        if (!abstract && keywords.length === 0) {
            return { success: false, reason: "No content found" };
        }
        
        await Zotero.DB.executeTransaction(async () => {
            let modified = false;
            
            if (abstract && (config.overwriteAbstract || !item.getField("abstractNote"))) {
                item.setField("abstractNote", abstract);
                modified = true;
                log(`${prefix} Added abstract to: ${title}`);
            }
            
            if (keywords.length > 0) {
                keywords.forEach(keyword => {
                    const tag = config.tagPrefix + keyword;
                    if (tag.length <= 255) {
                        item.addTag(tag);
                    }
                });
                modified = true;
                log(`${prefix} Added ${keywords.length} keywords to: ${title}`);
            }
            
            if (modified) {
                await item.save();
            }
        });
        
        return { success: true, documentType };
        
    } catch (error) {
        log(`${prefix} Error processing ${title}: ${error.message}`, 'error');
        return { success: false, reason: error.message };
    }
}

// --- MAIN EXECUTION ---
(async function main() {
    try {
        log("Starting Compact Abstract Extractor");
        
        if (typeof items === 'undefined' || !items.length) {
            return "No items selected. Please select items before running.";
        }
        
        if (items.length > 50) {
            return "Too many items (max 50 for compact version). Use full version for larger batches.";
        }
        
        const startTime = Date.now();
        let successCount = 0;
        const documentTypes = {};
        
        // Process items in small batches
        for (let i = 0; i < items.length; i += config.maxConcurrentItems) {
            const batch = items.slice(i, i + config.maxConcurrentItems);
            const promises = batch.map((item, idx) => processItem(item, i + idx, items.length));
            const results = await Promise.all(promises);
            
            results.forEach(result => {
                if (result.success) {
                    successCount++;
                    if (result.documentType) {
                        documentTypes[result.documentType] = (documentTypes[result.documentType] || 0) + 1;
                    }
                }
            });
            
            // Brief pause between batches
            if (i + config.maxConcurrentItems < items.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        
        const elapsed = Math.round((Date.now() - startTime) / 1000);
        const successRate = Math.round((successCount / items.length) * 100);
        
        let summary = `Compact Extractor Results:\n`;
        summary += `✓ Processed: ${successCount}/${items.length} items (${successRate}%)\n`;
        summary += `⏱ Time: ${elapsed}s\n`;
        
        if (Object.keys(documentTypes).length > 0) {
            summary += `📄 Document types: ${Object.entries(documentTypes).map(([type, count]) => `${type}(${count})`).join(', ')}\n`;
        }
        
        log(`Operation complete: ${successCount}/${items.length} items processed in ${elapsed}s`);
        return summary;
        
    } catch (error) {
        log(`Fatal error: ${error.message}`, 'error');
        return `Error: ${error.message}`;
    }
})();
