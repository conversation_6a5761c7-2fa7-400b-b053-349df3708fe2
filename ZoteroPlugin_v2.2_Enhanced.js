/**
 * Advanced Abstract and Keyword Extractor for Zotero v2.2
 * 
 * Phase 2 Enhancements in v2.2:
 * - Strategy Pattern for document-specific extraction
 * - Undo/Rollback functionality with change tracking
 * - Circuit Breaker pattern for enhanced error recovery
 * - Performance Analytics Dashboard
 * - Document type detection and classification
 * - Advanced error recovery mechanisms
 * 
 * @version 2.2.0
 * <AUTHOR>
 */

// --- CONFIGURATION PROFILES ---
const configProfiles = {
    'default': {
        overwriteAbstract: true,
        tagPrefix: "",
        maxConcurrentItems: 5,
        maxTextLength: 1000000,
        pdfExtractionTimeout: 30000,
        enableCaching: true,
        enableRetry: true,
        maxRetries: 3,
        enableUndo: true,
        enableAnalytics: true,
        enableCircuitBreaker: true,
        extractionStrategy: 'auto' // auto, academic, conference, technical, journal
    },
    'conservative': {
        overwriteAbstract: false,
        tagPrefix: "auto-conservative:",
        maxConcurrentItems: 2,
        pdfExtractionTimeout: 60000,
        enableCaching: true,
        enableRetry: true,
        maxRetries: 5,
        enableUndo: true,
        enableAnalytics: true,
        enableCircuitBreaker: true,
        extractionStrategy: 'academic'
    },
    'aggressive': {
        overwriteAbstract: true,
        tagPrefix: "auto:",
        maxConcurrentItems: 10,
        pdfExtractionTimeout: 15000,
        enableCaching: true,
        enableRetry: false,
        maxRetries: 1,
        enableUndo: false,
        enableAnalytics: true,
        enableCircuitBreaker: false,
        extractionStrategy: 'auto'
    },
    'academic': {
        overwriteAbstract: false,
        tagPrefix: "academic:",
        abstractLabels: ['Abstract', 'Summary', 'Executive Summary', 'Zusammenfassung', 'Résumé'],
        keywordLabels: ['Keywords', 'Index Terms', 'Subject Terms', 'Schlüsselwörter', 'Mots-clés'],
        maxConcurrentItems: 3,
        enableCaching: true,
        enableRetry: true,
        maxRetries: 3,
        enableUndo: true,
        enableAnalytics: true,
        enableCircuitBreaker: true,
        extractionStrategy: 'academic'
    },
    'production': {
        overwriteAbstract: false,
        tagPrefix: "prod:",
        maxConcurrentItems: 3,
        pdfExtractionTimeout: 45000,
        enableCaching: true,
        enableRetry: true,
        maxRetries: 4,
        enableUndo: true,
        enableAnalytics: true,
        enableCircuitBreaker: true,
        extractionStrategy: 'auto'
    }
};

// Select configuration profile
const ACTIVE_PROFILE = 'default';
const config = { ...configProfiles[ACTIVE_PROFILE] };

// Merge with base configuration
const baseConfig = {
    abstractLabels: ["Abstract", "Summary", "Zusammenfassung", "Résumé", "Resumen", "Samenvatting", "Riassunto"],
    keywordLabels: ["Keywords", "Key words", "Index Terms", "Schlüsselwörter", "Mots-clés", "Palabras clave", "Trefwoorden", "Parole chiave"],
};
Object.assign(config, { ...baseConfig, ...config });

// --- DOCUMENT TYPE DETECTOR ---
class DocumentTypeDetector {
    static detectType(text, metadata = {}) {
        const indicators = {
            academic: [
                /\babstract\b.*?\bintroduction\b/is,
                /\breferences\b.*?\bbibliography\b/is,
                /\bdoi:\s*10\./i,
                /\barxiv:/i,
                /\bpublished\s+in\b/i
            ],
            conference: [
                /\bproceedings\b/i,
                /\bconference\b/i,
                /\bacm\s+digital\s+library\b/i,
                /\bieee\s+xplore\b/i,
                /\bworkshop\b/i
            ],
            journal: [
                /\bjournal\s+of\b/i,
                /\bvolume\s+\d+.*?issue\s+\d+/i,
                /\bpages?\s+\d+[-–]\d+/i,
                /\bissn\b/i
            ],
            technical: [
                /\btechnical\s+report\b/i,
                /\bwhite\s+paper\b/i,
                /\bspecification\b/i,
                /\bstandard\b/i,
                /\bapi\s+documentation\b/i
            ],
            thesis: [
                /\bthesis\b/i,
                /\bdissertation\b/i,
                /\bphd\b/i,
                /\bmaster\s+of\b/i,
                /\buniversity\b/i
            ]
        };

        const scores = {};
        for (const [type, patterns] of Object.entries(indicators)) {
            scores[type] = patterns.reduce((score, pattern) => {
                return score + (pattern.test(text) ? 1 : 0);
            }, 0);
        }

        // Add metadata-based scoring
        if (metadata.itemType) {
            if (metadata.itemType === 'journalArticle') scores.journal = (scores.journal || 0) + 2;
            if (metadata.itemType === 'conferencePaper') scores.conference = (scores.conference || 0) + 2;
            if (metadata.itemType === 'thesis') scores.thesis = (scores.thesis || 0) + 2;
        }

        const maxScore = Math.max(...Object.values(scores));
        const detectedType = Object.keys(scores).find(type => scores[type] === maxScore);
        
        return {
            type: maxScore > 0 ? detectedType : 'generic',
            confidence: maxScore / Math.max(indicators[detectedType]?.length || 1, 1),
            scores
        };
    }
}

// --- EXTRACTION STRATEGY PATTERN ---
class ExtractionStrategy {
    constructor(name) {
        this.name = name;
    }

    extract() {
        throw new Error("Must implement extract method");
    }

    getAbstractPatterns() {
        throw new Error("Must implement getAbstractPatterns method");
    }

    getKeywordPatterns() {
        throw new Error("Must implement getKeywordPatterns method");
    }

    cleanAbstract(text) {
        return text
            .replace(/-\n\s*/g, '')
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s.,;:!?()-]/g, '')
            .trim()
            .substring(0, 10000);
    }

    cleanKeywords(text) {
        return text
            .trim()
            .split(/[;,.]\s*/)
            .map(k => k.trim().toLowerCase())
            .filter(k => k && k.length >= 2 && k.length <= 100)
            .filter((k, idx, self) => self.indexOf(k) === idx)
            .slice(0, 50);
    }
}

class AcademicPaperStrategy extends ExtractionStrategy {
    constructor() {
        super('academic');
    }

    getAbstractPatterns() {
        const labels = config.abstractLabels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,50}[\\s\\S]{0,4000}?)(?=\\n\\s*(?:Keywords|1\\.?\\s+Introduction|I\\.\\s+INTRODUCTION))`, 'i'),
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([\\s\\S]{50,3000}?)(?=\\n\\s*[A-Z][A-Z\\s]{3,20}\\n)`, 'i')
        ];
    }

    getKeywordPatterns() {
        const labels = config.keywordLabels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,500})`, 'i'),
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([\\s\\S]{1,200}?)(?=\\n\\s*[A-Z])`, 'i')
        ];
    }

    extract(text) {
        let abstract = "";
        let keywords = [];

        // Try multiple patterns for abstract
        for (const pattern of this.getAbstractPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                abstract = this.cleanAbstract(match[1]);
                break;
            }
        }

        // Try multiple patterns for keywords
        for (const pattern of this.getKeywordPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                keywords = this.cleanKeywords(match[1]);
                break;
            }
        }

        return { abstract, keywords };
    }
}

class ConferencePaperStrategy extends ExtractionStrategy {
    constructor() {
        super('conference');
    }

    getAbstractPatterns() {
        const labels = config.abstractLabels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([\\s\\S]{50,2500}?)(?=\\n\\s*(?:Keywords|Categories|1\\.))`, 'i'),
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,30}[\\s\\S]{0,2000}?)(?=\\n\\s*[A-Z])`, 'i')
        ];
    }

    getKeywordPatterns() {
        const labels = [...config.keywordLabels, 'Categories', 'Subject Terms', 'ACM Categories'].map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,400})`, 'i'),
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([\\s\\S]{1,300}?)(?=\\n\\s*[A-Z])`, 'i')
        ];
    }

    extract(text) {
        let abstract = "";
        let keywords = [];

        for (const pattern of this.getAbstractPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                abstract = this.cleanAbstract(match[1]);
                break;
            }
        }

        for (const pattern of this.getKeywordPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                keywords = this.cleanKeywords(match[1]);
                break;
            }
        }

        return { abstract, keywords };
    }
}

class TechnicalReportStrategy extends ExtractionStrategy {
    constructor() {
        super('technical');
    }

    getAbstractPatterns() {
        const labels = [...config.abstractLabels, 'Executive Summary', 'Overview', 'Summary'].map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([\\s\\S]{50,5000}?)(?=\\n\\s*(?:Table of Contents|Contents|1\\.))`, 'i'),
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,50}[\\s\\S]{0,3000}?)(?=\\n\\s*[A-Z])`, 'i')
        ];
    }

    getKeywordPatterns() {
        const labels = [...config.keywordLabels, 'Tags', 'Topics', 'Subject Areas'].map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,600})`, 'i')
        ];
    }

    extract(text) {
        let abstract = "";
        let keywords = [];

        for (const pattern of this.getAbstractPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                abstract = this.cleanAbstract(match[1]);
                break;
            }
        }

        for (const pattern of this.getKeywordPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                keywords = this.cleanKeywords(match[1]);
                break;
            }
        }

        return { abstract, keywords };
    }
}

class GenericStrategy extends ExtractionStrategy {
    constructor() {
        super('generic');
    }

    getAbstractPatterns() {
        const labels = config.abstractLabels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,50}[\\s\\S]{0,3000}?)(?=\\n\\s*[A-Z])`, 'i')
        ];
    }

    getKeywordPatterns() {
        const labels = config.keywordLabels.map(label => label.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        return [
            new RegExp(`\\n\\s*(?:${labels})\\s*[:\\-\\s\\n]([^\\n]{1,500})`, 'i')
        ];
    }

    extract(text) {
        let abstract = "";
        let keywords = [];

        for (const pattern of this.getAbstractPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                abstract = this.cleanAbstract(match[1]);
                break;
            }
        }

        for (const pattern of this.getKeywordPatterns()) {
            const match = text.match(pattern);
            if (match && match[1]) {
                keywords = this.cleanKeywords(match[1]);
                break;
            }
        }

        return { abstract, keywords };
    }
}

// --- EXTRACTION STRATEGY FACTORY ---
class ExtractionStrategyFactory {
    static createStrategy(strategyType, documentType = null) {
        if (strategyType === 'auto' && documentType) {
            switch (documentType) {
                case 'academic':
                case 'journal':
                    return new AcademicPaperStrategy();
                case 'conference':
                    return new ConferencePaperStrategy();
                case 'technical':
                    return new TechnicalReportStrategy();
                default:
                    return new GenericStrategy();
            }
        }

        switch (strategyType) {
            case 'academic':
                return new AcademicPaperStrategy();
            case 'conference':
                return new ConferencePaperStrategy();
            case 'technical':
                return new TechnicalReportStrategy();
            default:
                return new GenericStrategy();
        }
    }
}

// --- UNDO/ROLLBACK SYSTEM ---
class ChangeTracker {
    constructor() {
        this.changes = [];
        this.sessionId = Date.now().toString();
        this.maxChanges = 1000; // Limit memory usage
    }

    recordChange(itemId, field, oldValue, newValue, action = 'modify') {
        const change = {
            id: `${this.sessionId}_${this.changes.length}`,
            timestamp: Date.now(),
            itemId,
            field,
            oldValue,
            newValue,
            action
        };

        this.changes.push(change);

        // Limit memory usage
        if (this.changes.length > this.maxChanges) {
            this.changes.shift();
        }

        log(`Recorded change: ${action} ${field} for item ${itemId}`, 'info');
        return change.id;
    }

    recordTagAddition(itemId, tag) {
        return this.recordChange(itemId, 'tags', null, tag, 'addTag');
    }

    async rollback(changeId = null) {
        const changesToRollback = changeId
            ? this.changes.filter(c => c.id === changeId)
            : [...this.changes].reverse();

        if (changesToRollback.length === 0) {
            log('No changes to rollback', 'warn');
            return { success: false, message: 'No changes found' };
        }

        let rolledBackCount = 0;
        const errors = [];

        for (const change of changesToRollback) {
            try {
                const item = await Zotero.Items.getAsync(change.itemId);
                if (!item) {
                    errors.push(`Item ${change.itemId} not found`);
                    continue;
                }

                await Zotero.DB.executeTransaction(async () => {
                    switch (change.action) {
                        case 'modify':
                            if (change.oldValue !== null) {
                                item.setField(change.field, change.oldValue);
                            } else {
                                item.setField(change.field, '');
                            }
                            break;
                        case 'addTag':
                            item.removeTag(change.newValue);
                            break;
                    }
                    await item.save();
                });

                rolledBackCount++;
                log(`Rolled back change ${change.id} for item ${change.itemId}`, 'info');
            } catch (error) {
                errors.push(`Failed to rollback change ${change.id}: ${error.message}`);
                log(`Rollback error for change ${change.id}: ${error.message}`, 'error');
            }
        }

        // Remove successfully rolled back changes
        if (!changeId) {
            this.changes = [];
        } else {
            this.changes = this.changes.filter(c => c.id !== changeId);
        }

        return {
            success: rolledBackCount > 0,
            rolledBackCount,
            errors,
            message: `Rolled back ${rolledBackCount} changes${errors.length > 0 ? ` with ${errors.length} errors` : ''}`
        };
    }

    getChangesSummary() {
        const summary = {
            totalChanges: this.changes.length,
            itemsModified: new Set(this.changes.map(c => c.itemId)).size,
            changesByType: {},
            sessionId: this.sessionId
        };

        this.changes.forEach(change => {
            summary.changesByType[change.action] = (summary.changesByType[change.action] || 0) + 1;
        });

        return summary;
    }

    exportChanges() {
        return {
            sessionId: this.sessionId,
            timestamp: Date.now(),
            changes: this.changes,
            summary: this.getChangesSummary()
        };
    }
}

// --- CIRCUIT BREAKER PATTERN ---
class CircuitBreaker {
    constructor(threshold = 5, timeout = 60000, monitorWindow = 300000) {
        this.failureThreshold = threshold;
        this.timeout = timeout;
        this.monitorWindow = monitorWindow;
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
        this.successCount = 0;
        this.recentFailures = [];
    }

    async execute(operation, operationName = 'operation') {
        this.cleanOldFailures();

        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';
                this.successCount = 0;
                log(`Circuit breaker transitioning to HALF_OPEN for ${operationName}`, 'info');
            } else {
                throw new Error(`Circuit breaker is OPEN for ${operationName}. Try again in ${Math.round((this.timeout - (Date.now() - this.lastFailureTime)) / 1000)}s`);
            }
        }

        try {
            const result = await operation();
            this.onSuccess(operationName);
            return result;
        } catch (error) {
            this.onFailure(operationName, error);
            throw error;
        }
    }

    onSuccess(operationName) {
        if (this.state === 'HALF_OPEN') {
            this.successCount++;
            if (this.successCount >= 3) { // Require 3 successes to close
                this.state = 'CLOSED';
                this.failureCount = 0;
                this.recentFailures = [];
                log(`Circuit breaker CLOSED for ${operationName} after successful recovery`, 'info');
            }
        } else if (this.state === 'CLOSED') {
            // Reset failure count on success
            this.failureCount = Math.max(0, this.failureCount - 1);
        }
    }

    onFailure(operationName, error) {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        this.recentFailures.push({
            timestamp: Date.now(),
            error: error.message
        });

        if (this.state === 'HALF_OPEN') {
            this.state = 'OPEN';
            log(`Circuit breaker OPEN for ${operationName} - failed during recovery`, 'warn');
        } else if (this.failureCount >= this.failureThreshold) {
            this.state = 'OPEN';
            log(`Circuit breaker OPEN for ${operationName} - threshold exceeded (${this.failureCount}/${this.failureThreshold})`, 'warn');
        }
    }

    cleanOldFailures() {
        const cutoff = Date.now() - this.monitorWindow;
        this.recentFailures = this.recentFailures.filter(f => f.timestamp > cutoff);

        // Adjust failure count based on recent failures
        this.failureCount = this.recentFailures.length;
    }

    getStatus() {
        return {
            state: this.state,
            failureCount: this.failureCount,
            recentFailures: this.recentFailures.length,
            lastFailureTime: this.lastFailureTime,
            timeUntilRetry: this.state === 'OPEN' ? Math.max(0, this.timeout - (Date.now() - this.lastFailureTime)) : 0
        };
    }
}

// --- PERFORMANCE ANALYTICS ---
class PerformanceAnalytics {
    constructor() {
        this.metrics = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            totalProcessingTime: 0,
            averageProcessingTime: 0,
            operationTimes: [],
            errorTypes: {},
            documentTypes: {},
            extractionStrategies: {},
            cachePerformance: {
                hits: 0,
                misses: 0,
                hitRate: 0
            },
            circuitBreakerEvents: [],
            sessionStart: Date.now(),
            lastUpdate: Date.now()
        };
    }

    recordOperation(success, processingTime, errorType = null, documentType = null, strategy = null) {
        this.metrics.totalOperations++;
        this.metrics.lastUpdate = Date.now();

        if (success) {
            this.metrics.successfulOperations++;
        } else {
            this.metrics.failedOperations++;
            if (errorType) {
                this.metrics.errorTypes[errorType] = (this.metrics.errorTypes[errorType] || 0) + 1;
            }
        }

        if (processingTime) {
            this.metrics.totalProcessingTime += processingTime;
            this.metrics.operationTimes.push(processingTime);

            // Keep only last 100 times for memory efficiency
            if (this.metrics.operationTimes.length > 100) {
                this.metrics.operationTimes.shift();
            }

            this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.totalOperations;
        }

        if (documentType) {
            this.metrics.documentTypes[documentType] = (this.metrics.documentTypes[documentType] || 0) + 1;
        }

        if (strategy) {
            this.metrics.extractionStrategies[strategy] = (this.metrics.extractionStrategies[strategy] || 0) + 1;
        }
    }

    recordCachePerformance(hits, misses) {
        this.metrics.cachePerformance.hits = hits;
        this.metrics.cachePerformance.misses = misses;
        const total = hits + misses;
        this.metrics.cachePerformance.hitRate = total > 0 ? Math.round((hits / total) * 100) : 0;
    }

    recordCircuitBreakerEvent(event, operationName) {
        this.metrics.circuitBreakerEvents.push({
            timestamp: Date.now(),
            event,
            operation: operationName
        });

        // Keep only last 50 events
        if (this.metrics.circuitBreakerEvents.length > 50) {
            this.metrics.circuitBreakerEvents.shift();
        }
    }

    getReport() {
        const sessionDuration = Date.now() - this.metrics.sessionStart;
        const successRate = this.metrics.totalOperations > 0
            ? Math.round((this.metrics.successfulOperations / this.metrics.totalOperations) * 100)
            : 0;

        return {
            summary: {
                sessionDuration: Math.round(sessionDuration / 1000),
                totalOperations: this.metrics.totalOperations,
                successRate,
                averageProcessingTime: Math.round(this.metrics.averageProcessingTime),
                operationsPerMinute: sessionDuration > 0 ? Math.round((this.metrics.totalOperations / sessionDuration) * 60000) : 0
            },
            performance: {
                totalProcessingTime: Math.round(this.metrics.totalProcessingTime / 1000),
                averageTime: Math.round(this.metrics.averageProcessingTime),
                medianTime: this.calculateMedian(this.metrics.operationTimes),
                p95Time: this.calculatePercentile(this.metrics.operationTimes, 95)
            },
            errors: {
                totalErrors: this.metrics.failedOperations,
                errorTypes: this.metrics.errorTypes,
                errorRate: this.metrics.totalOperations > 0
                    ? Math.round((this.metrics.failedOperations / this.metrics.totalOperations) * 100)
                    : 0
            },
            extraction: {
                documentTypes: this.metrics.documentTypes,
                strategies: this.metrics.extractionStrategies
            },
            cache: this.metrics.cachePerformance,
            circuitBreaker: {
                totalEvents: this.metrics.circuitBreakerEvents.length,
                recentEvents: this.metrics.circuitBreakerEvents.slice(-10)
            }
        };
    }

    calculateMedian(times) {
        if (times.length === 0) return 0;
        const sorted = [...times].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0
            ? Math.round((sorted[mid - 1] + sorted[mid]) / 2)
            : Math.round(sorted[mid]);
    }

    calculatePercentile(times, percentile) {
        if (times.length === 0) return 0;
        const sorted = [...times].sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return Math.round(sorted[Math.max(0, index)]);
    }

    exportReport() {
        return {
            timestamp: Date.now(),
            version: '2.2.0',
            profile: ACTIVE_PROFILE,
            report: this.getReport(),
            rawMetrics: this.metrics
        };
    }
}

// --- ENHANCED CACHING SYSTEM (from v2.1) ---
class PDFCache {
    constructor(maxSize = 50) {
        this.cache = new Map();
        this.maxSize = maxSize;
        this.accessOrder = [];
        this.stats = { hits: 0, misses: 0 };
    }

    generateKey(attachmentId, lastModified) {
        return `${attachmentId}_${lastModified || 'unknown'}`;
    }

    get(attachmentId, lastModified) {
        const key = this.generateKey(attachmentId, lastModified);
        if (this.cache.has(key)) {
            this.updateAccessOrder(key);
            this.stats.hits++;
            return this.cache.get(key);
        }
        this.stats.misses++;
        return null;
    }

    set(attachmentId, lastModified, data) {
        const key = this.generateKey(attachmentId, lastModified);
        if (this.cache.size >= this.maxSize) {
            this.evictLeastRecentlyUsed();
        }
        this.cache.set(key, { data, timestamp: Date.now() });
        this.updateAccessOrder(key);
    }

    updateAccessOrder(key) {
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
            this.accessOrder.splice(index, 1);
        }
        this.accessOrder.push(key);
    }

    evictLeastRecentlyUsed() {
        if (this.accessOrder.length > 0) {
            const lruKey = this.accessOrder.shift();
            this.cache.delete(lruKey);
        }
    }

    getStats() {
        const total = this.stats.hits + this.stats.misses;
        return {
            ...this.stats,
            hitRate: total > 0 ? Math.round((this.stats.hits / total) * 100) : 0,
            cacheSize: this.cache.size
        };
    }
}

// --- RETRY MECHANISM (from v2.1) ---
class RetryManager {
    static async withRetry(operation, maxRetries = 3, baseDelay = 1000, operationName = 'operation') {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                if (attempt === maxRetries) {
                    log(`${operationName} failed after ${maxRetries} attempts: ${error.message}`, 'error');
                    throw error;
                }

                const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000; // Add jitter
                log(`${operationName} attempt ${attempt} failed, retrying in ${Math.round(delay)}ms: ${error.message}`, 'warn');
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
}

// --- PROGRESS TRACKER (Enhanced for v2.2) ---
class ProgressTracker {
    constructor(totalItems) {
        this.totalItems = totalItems;
        this.processedItems = 0;
        this.successfulItems = 0;
        this.startTime = Date.now();
        this.itemTimes = [];
        this.lastUpdate = Date.now();
        this.documentTypes = {};
        this.strategies = {};
    }

    updateProgress(success = false, processingTime = null, documentType = null, strategy = null) {
        this.processedItems++;
        if (success) this.successfulItems++;
        if (processingTime) this.itemTimes.push(processingTime);

        if (documentType) {
            this.documentTypes[documentType] = (this.documentTypes[documentType] || 0) + 1;
        }

        if (strategy) {
            this.strategies[strategy] = (this.strategies[strategy] || 0) + 1;
        }

        // Update every 2 seconds or on completion
        const now = Date.now();
        if (now - this.lastUpdate > 2000 || this.processedItems === this.totalItems) {
            this.displayProgress();
            this.lastUpdate = now;
        }
    }

    displayProgress() {
        const percentage = Math.round((this.processedItems / this.totalItems) * 100);
        const elapsed = Date.now() - this.startTime;

        let eta = 'calculating...';
        if (this.itemTimes.length > 0) {
            const avgTime = this.itemTimes.reduce((a, b) => a + b, 0) / this.itemTimes.length;
            const remainingItems = this.totalItems - this.processedItems;
            const etaMs = remainingItems * avgTime;
            eta = etaMs > 0 ? `${Math.round(etaMs / 1000)}s` : 'complete';
        }

        log(`Progress: ${this.processedItems}/${this.totalItems} (${percentage}%) | ` +
            `Success: ${this.successfulItems} | ` +
            `Elapsed: ${Math.round(elapsed / 1000)}s | ` +
            `ETA: ${eta}`);
    }

    getFinalReport() {
        const elapsed = Date.now() - this.startTime;
        const successRate = Math.round((this.successfulItems / this.totalItems) * 100);

        return {
            total: this.totalItems,
            processed: this.processedItems,
            successful: this.successfulItems,
            failed: this.processedItems - this.successfulItems,
            successRate,
            elapsedTime: Math.round(elapsed / 1000),
            avgTimePerItem: this.itemTimes.length > 0 ?
                Math.round(this.itemTimes.reduce((a, b) => a + b, 0) / this.itemTimes.length / 1000) : 0,
            documentTypes: this.documentTypes,
            strategies: this.strategies
        };
    }
}

// --- GLOBAL INSTANCES ---
const pdfCache = config.enableCaching ? new PDFCache() : null;
const changeTracker = config.enableUndo ? new ChangeTracker() : null;
const circuitBreaker = config.enableCircuitBreaker ? new CircuitBreaker() : null;
const analytics = config.enableAnalytics ? new PerformanceAnalytics() : null;

// --- ZOTERO API INITIALIZATION ---
let Zotero;
try {
    if (typeof window !== 'undefined' && window.Zotero) {
        Zotero = window.Zotero;
    }
    // For older versions - this approach is less likely to work in newer Zotero
    else if (typeof Components !== 'undefined') {
        Zotero = Components.classes['@zotero.org/Zotero;1']
            .getService(Components.interfaces.nsISupports).wrappedJSObject;
    }
    else {
        throw new Error("Could not find Zotero in expected locations");
    }
} catch (error) {
    throw new Error(`Failed to initialize Zotero API: ${error.message}`);
}

if (!Zotero) {
    throw new Error("Could not access Zotero API. Please ensure Zotero is running.");
}

// --- ENHANCED LOGGING ---
function log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[Abstract Extractor v2.2 ${timestamp}]`;

    switch (level) {
        case 'error':
            Zotero.logError(`${prefix} ERROR: ${message}`);
            break;
        case 'warn':
            Zotero.debug(`${prefix} WARNING: ${message}`, 2);
            break;
        default:
            Zotero.debug(`${prefix} ${message}`);
    }
}

// --- TEXT PREPROCESSING (Enhanced for v2.2) ---
class TextPreprocessor {
    static preprocess(text, documentType = 'generic') {
        if (!text || typeof text !== 'string') return '';

        // Stage 1: Remove headers/footers and page numbers
        text = this.removeHeadersFooters(text);

        // Stage 2: Document-type specific preprocessing
        text = this.documentSpecificPreprocessing(text, documentType);

        // Stage 3: Extract relevant sections
        text = this.extractRelevantSections(text, documentType);

        // Stage 4: Normalize whitespace and clean up
        text = this.normalizeText(text);

        return text;
    }

    static removeHeadersFooters(text) {
        return text
            .replace(/^.{0,100}(?:page \d+|p\.\s*\d+).{0,50}$/gim, '')
            .replace(/^.{0,50}(?:doi:|DOI:).{0,100}$/gim, '')
            .replace(/^.{0,50}(?:©|copyright).{0,100}$/gim, '');
    }

    static documentSpecificPreprocessing(text, documentType) {
        switch (documentType) {
            case 'academic':
            case 'journal':
                // Remove common academic paper artifacts
                text = text.replace(/\b(?:received|accepted|published)\s+\d{1,2}\s+\w+\s+\d{4}\b/gi, '');
                break;
            case 'conference':
                // Remove conference-specific artifacts
                text = text.replace(/\b(?:ACM|IEEE)\s+\d{4}\b/gi, '');
                break;
            case 'technical':
                // Remove technical report artifacts
                text = text.replace(/\b(?:version|revision)\s+\d+\.\d+\b/gi, '');
                break;
        }
        return text;
    }

    static extractRelevantSections(text, documentType) {
        const textLength = text.length;
        if (textLength < 5000) return text;

        // Adjust extraction based on document type
        let frontPercent = 0.3;
        let backPercent = 0.2;

        if (documentType === 'technical') {
            frontPercent = 0.4; // Technical reports often have longer introductions
            backPercent = 0.1;
        } else if (documentType === 'conference') {
            frontPercent = 0.25; // Conference papers are usually more concise
            backPercent = 0.15;
        }

        const frontMatter = text.substring(0, Math.floor(textLength * frontPercent));
        const backMatter = text.substring(Math.floor(textLength * (1 - backPercent)));

        return frontMatter + '\n\n' + backMatter;
    }

    static normalizeText(text) {
        return text
            .replace(/\r\n/g, '\n')
            .replace(/\n{3,}/g, '\n\n')
            .replace(/[ \t]{2,}/g, ' ')
            .trim();
    }
}

// --- ENHANCED EXTRACTION FUNCTIONS ---
async function extractPdfTextSafely(attachmentId, timeout = config.pdfExtractionTimeout) {
    // Check cache first
    if (pdfCache) {
        const cached = pdfCache.get(attachmentId, 'current');
        if (cached) {
            log(`Using cached PDF text for attachment ${attachmentId}`);
            return cached.data;
        }
    }

    const extractOperation = async () => {
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`PDF text extraction timed out after ${timeout}ms`));
            }, timeout);

            try {
                const result = await Zotero.PDFWorker.getFullText(attachmentId, 2);
                clearTimeout(timeoutId);

                if (!result || typeof result.text !== 'string') {
                    reject(new Error('PDF text extraction returned invalid result'));
                    return;
                }

                // Limit text length
                const finalText = result.text.length > config.maxTextLength
                    ? result.text.substring(0, config.maxTextLength)
                    : result.text;

                resolve(finalText);
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    };

    try {
        const operation = circuitBreaker
            ? () => circuitBreaker.execute(extractOperation, `PDF extraction for ${attachmentId}`)
            : extractOperation;

        const text = config.enableRetry
            ? await RetryManager.withRetry(operation, config.maxRetries, 1000, `PDF extraction for ${attachmentId}`)
            : await operation();

        // Cache the result
        if (pdfCache && text) {
            pdfCache.set(attachmentId, 'current', text);
        }

        return text;
    } catch (error) {
        log(`Failed to extract PDF text for attachment ${attachmentId}: ${error.message}`, 'error');
        return "";
    }
}

// --- ENHANCED TESTING FRAMEWORK ---
class TestFramework {
    constructor() {
        this.tests = [];
    }

    addTest(name, testFunction) {
        this.tests.push({ name, testFunction });
    }

    async runTests() {
        log('Running enhanced test suite for v2.2...');
        const results = [];

        for (const test of this.tests) {
            try {
                await test.testFunction();
                results.push({ name: test.name, status: 'PASS' });
                log(`✓ ${test.name}`, 'info');
            } catch (error) {
                results.push({ name: test.name, status: 'FAIL', error: error.message });
                log(`✗ ${test.name}: ${error.message}`, 'error');
            }
        }

        const passed = results.filter(r => r.status === 'PASS').length;
        log(`Tests completed: ${passed}/${results.length} passed`);

        return results;
    }
}

// Initialize enhanced test framework
const testFramework = new TestFramework();

// Add v2.2 specific tests
testFramework.addTest('Document Type Detection', async () => {
    const academicText = 'Abstract: This paper presents... Keywords: machine learning, AI';
    const detection = DocumentTypeDetector.detectType(academicText);
    if (detection.type !== 'academic') {
        throw new Error(`Expected 'academic', got '${detection.type}'`);
    }
});

testFramework.addTest('Strategy Pattern', async () => {
    const strategy = ExtractionStrategyFactory.createStrategy('academic');
    if (!(strategy instanceof AcademicPaperStrategy)) {
        throw new Error('Strategy factory failed to create academic strategy');
    }
});

testFramework.addTest('Change Tracker', async () => {
    if (!changeTracker) return; // Skip if disabled

    changeTracker.recordChange(123, 'abstractNote', 'old', 'new');
    const summary = changeTracker.getChangesSummary();
    if (summary.totalChanges !== 1) {
        throw new Error('Change tracking failed');
    }
});

testFramework.addTest('Circuit Breaker', async () => {
    if (!circuitBreaker) return; // Skip if disabled

    const status = circuitBreaker.getStatus();
    if (status.state !== 'CLOSED') {
        throw new Error('Circuit breaker should start in CLOSED state');
    }
});

testFramework.addTest('Performance Analytics', async () => {
    if (!analytics) return; // Skip if disabled

    analytics.recordOperation(true, 1000, null, 'academic', 'academic');
    const report = analytics.getReport();
    if (report.summary.totalOperations !== 1) {
        throw new Error('Analytics recording failed');
    }
});

// --- MAIN EXECUTION ---
async function main() {
    try {
        log(`Starting Advanced Abstract Extractor v2.2 with profile: ${ACTIVE_PROFILE}`);

        // Run tests if in development mode
        if (config.runTests) {
            await testFramework.runTests();
        }

        // Use the items variable passed to the script (recommended approach)
        // This variable should be available in the script context when run from Zotero
        if (typeof items === 'undefined') {
            return "No items available. This script should be run from Zotero with selected items.";
        }

        if (!items || !Array.isArray(items) || !items.length) {
            return "No items selected. Please select items in Zotero before running this script.";
        }

        if (items.length > 100) {
            return "Too many items selected (maximum 100). Please select fewer items.";
        }

        // Enhanced confirmation dialog
        let promptService;
        try {
            promptService = Components.classes["@mozilla.org/embedcomp/prompt-service;1"]
                .getService(Components.interfaces.nsIPromptService);
        } catch (error) {
            log(`Could not access prompt service: ${error.message}`, 'error');
            return "Could not display confirmation dialog. Operation cancelled for safety.";
        }

        const confirmMessage = `Advanced Abstract Extractor v2.2\n\n` +
            `Profile: ${ACTIVE_PROFILE}\n` +
            `Items to process: ${items.length}\n\n` +
            `New v2.2 Features:\n` +
            `• Document type detection: ${config.extractionStrategy}\n` +
            `• Undo/Rollback: ${config.enableUndo ? 'Enabled' : 'Disabled'}\n` +
            `• Circuit breaker: ${config.enableCircuitBreaker ? 'Enabled' : 'Disabled'}\n` +
            `• Performance analytics: ${config.enableAnalytics ? 'Enabled' : 'Disabled'}\n\n` +
            `Configuration:\n` +
            `• Overwrite existing abstracts: ${config.overwriteAbstract ? 'Yes' : 'No'}\n` +
            `• Tag prefix: "${config.tagPrefix}"\n` +
            `• Max concurrent: ${config.maxConcurrentItems}\n` +
            `• Caching enabled: ${config.enableCaching ? 'Yes' : 'No'}\n` +
            `• Retry enabled: ${config.enableRetry ? 'Yes' : 'No'}\n\n` +
            `This will modify your library. Continue?`;

        if (!promptService.confirm(null, "Abstract Extractor v2.2", confirmMessage)) {
            return "Operation cancelled by user.";
        }

        // Initialize progress tracker
        const progressTracker = new ProgressTracker(items.length);

        // Process items with enhanced v2.2 features
        const results = [];
        for (let i = 0; i < items.length; i += config.maxConcurrentItems) {
            const batch = items.slice(i, i + config.maxConcurrentItems);
            const batchPromises = batch.map(async (item, batchIndex) => {
                const itemIndex = i + batchIndex;
                const startTime = Date.now();

                try {
                    const result = await processItemEnhanced(item, itemIndex, items.length);
                    const processingTime = Date.now() - startTime;

                    // Record analytics
                    if (analytics) {
                        analytics.recordOperation(
                            result.success,
                            processingTime,
                            result.success ? null : result.reason,
                            result.documentType,
                            result.strategy
                        );
                    }

                    progressTracker.updateProgress(result.success, processingTime, result.documentType, result.strategy);
                    return result;
                } catch (error) {
                    const processingTime = Date.now() - startTime;

                    if (analytics) {
                        analytics.recordOperation(false, processingTime, 'unexpected_error');
                    }

                    progressTracker.updateProgress(false, processingTime);
                    return { success: false, reason: `Unexpected error: ${error.message}` };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // Brief pause between batches
            if (i + config.maxConcurrentItems < items.length) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // Generate comprehensive final report
        const finalReport = progressTracker.getFinalReport();
        const cacheStats = pdfCache ? pdfCache.getStats() : null;
        const analyticsReport = analytics ? analytics.getReport() : null;
        const changesSummary = changeTracker ? changeTracker.getChangesSummary() : null;
        const circuitBreakerStatus = circuitBreaker ? circuitBreaker.getStatus() : null;

        // Update analytics with cache performance
        if (analytics && cacheStats) {
            analytics.recordCachePerformance(cacheStats.hits, cacheStats.misses);
        }

        let summaryMessage = `Abstract & Keyword Extraction Complete (v2.2)\n\n`;
        summaryMessage += `Results:\n`;
        summaryMessage += `✓ Successfully processed: ${finalReport.successful} items\n`;
        summaryMessage += `✗ Failed/Skipped: ${finalReport.failed} items\n`;
        summaryMessage += `📊 Success rate: ${finalReport.successRate}%\n`;
        summaryMessage += `⏱️ Total time: ${finalReport.elapsedTime}s\n`;
        summaryMessage += `⚡ Avg time per item: ${finalReport.avgTimePerItem}s\n\n`;

        if (Object.keys(finalReport.documentTypes).length > 0) {
            summaryMessage += `Document Types Detected:\n`;
            Object.entries(finalReport.documentTypes).forEach(([type, count]) => {
                summaryMessage += `• ${type}: ${count} items\n`;
            });
            summaryMessage += `\n`;
        }

        if (Object.keys(finalReport.strategies).length > 0) {
            summaryMessage += `Extraction Strategies Used:\n`;
            Object.entries(finalReport.strategies).forEach(([strategy, count]) => {
                summaryMessage += `• ${strategy}: ${count} items\n`;
            });
            summaryMessage += `\n`;
        }

        if (cacheStats) {
            summaryMessage += `Cache Performance:\n`;
            summaryMessage += `📈 Hit rate: ${cacheStats.hitRate}%\n`;
            summaryMessage += `💾 Cache size: ${cacheStats.cacheSize} items\n\n`;
        }

        if (changesSummary && changesSummary.totalChanges > 0) {
            summaryMessage += `Changes Made (Undo Available):\n`;
            summaryMessage += `📝 Total changes: ${changesSummary.totalChanges}\n`;
            summaryMessage += `📄 Items modified: ${changesSummary.itemsModified}\n\n`;
        }

        if (circuitBreakerStatus && circuitBreakerStatus.recentFailures > 0) {
            summaryMessage += `Circuit Breaker Status:\n`;
            summaryMessage += `🔴 State: ${circuitBreakerStatus.state}\n`;
            summaryMessage += `⚠️ Recent failures: ${circuitBreakerStatus.recentFailures}\n\n`;
        }

        summaryMessage += `Profile used: ${ACTIVE_PROFILE}\n`;
        summaryMessage += `Check debug log for detailed information.`;

        // Log comprehensive results
        log(`Operation complete. Final report: ${JSON.stringify(finalReport)}`);
        if (cacheStats) log(`Cache statistics: ${JSON.stringify(cacheStats)}`);
        if (analyticsReport) log(`Analytics report: ${JSON.stringify(analyticsReport.summary)}`);
        if (changesSummary) log(`Changes summary: ${JSON.stringify(changesSummary)}`);

        try {
            promptService.alert(null, "Abstract Extractor v2.2 - Results", summaryMessage);
        } catch (error) {
            log(`Could not display results dialog: ${error.message}`, 'warn');
        }

        return `Processed ${finalReport.successful}/${items.length} items (${finalReport.successRate}% success). Time: ${finalReport.elapsedTime}s. Profile: ${ACTIVE_PROFILE}`;

    } catch (error) {
        log(`Fatal error in main execution: ${error.message}`, 'error');
        return `Fatal error: ${error.message}`;
    }
}

// Execute the main function
// In Zotero scripts, the 'items' variable is automatically available
// when the script is run with selected items
main();

// --- ENHANCED ITEM PROCESSING FUNCTION ---
async function processItemEnhanced(item, index, totalItems) {
    const progressPrefix = `[${index + 1}/${totalItems}]`;
    let itemTitle = "Unknown";

    try {
        itemTitle = item.getDisplayTitle() || `Item ${item.id}`;

        // Validate item
        if (!item || !item.isRegularItem()) {
            log(`${progressPrefix} Skipping non-regular item: ${itemTitle}`);
            return { success: false, reason: "Not a regular item" };
        }

        // Find PDF attachment
        const attachment = await item.getBestAttachment();
        if (!attachment) {
            return { success: false, reason: "No attachment found" };
        }

        const contentType = attachment.attachmentContentType || attachment.contentType || "";
        if (contentType !== 'application/pdf') {
            return { success: false, reason: "No PDF attachment" };
        }

        // Extract PDF text with v2.2 enhancements
        const rawPdfText = await extractPdfTextSafely(attachment.id);
        if (!rawPdfText) {
            return { success: false, reason: "PDF text extraction failed" };
        }

        // Detect document type (v2.2 feature)
        const itemMetadata = {
            itemType: item.itemType,
            title: itemTitle
        };
        const documentDetection = DocumentTypeDetector.detectType(rawPdfText, itemMetadata);
        const documentType = documentDetection.type;

        log(`${progressPrefix} Detected document type: ${documentType} (confidence: ${Math.round(documentDetection.confidence * 100)}%)`, 'info');

        // Preprocess text based on document type
        const preprocessedText = TextPreprocessor.preprocess(rawPdfText, documentType);

        // Select extraction strategy (v2.2 feature)
        const strategy = ExtractionStrategyFactory.createStrategy(config.extractionStrategy, documentType);
        log(`${progressPrefix} Using extraction strategy: ${strategy.name}`, 'info');

        // Extract content using strategy pattern
        const extractionResult = strategy.extract(preprocessedText);
        const foundAbstract = extractionResult.abstract;
        const foundKeywords = extractionResult.keywords;

        if (!foundAbstract && foundKeywords.length === 0) {
            return {
                success: false,
                reason: "No extractable content found",
                documentType,
                strategy: strategy.name
            };
        }

        // Update item with change tracking (v2.2 feature)
        await Zotero.DB.executeTransaction(async () => {
            let modified = false;

            if (foundAbstract) {
                const existingAbstract = item.getField("abstractNote");
                if (config.overwriteAbstract || !existingAbstract) {
                    // Record change for undo functionality
                    if (changeTracker) {
                        changeTracker.recordChange(item.id, 'abstractNote', existingAbstract, foundAbstract);
                    }

                    item.setField("abstractNote", foundAbstract);
                    modified = true;
                    log(`${progressPrefix} Added abstract (${foundAbstract.length} chars) to: ${itemTitle}`);
                } else {
                    log(`${progressPrefix} Skipped abstract (already exists) for: ${itemTitle}`);
                }
            }

            if (foundKeywords.length > 0) {
                let addedKeywords = 0;
                foundKeywords.forEach(keyword => {
                    try {
                        const tagWithPrefix = config.tagPrefix + keyword;
                        if (tagWithPrefix.length <= 255) {
                            // Record change for undo functionality
                            if (changeTracker) {
                                changeTracker.recordTagAddition(item.id, tagWithPrefix);
                            }

                            item.addTag(tagWithPrefix);
                            addedKeywords++;
                        } else {
                            log(`${progressPrefix} Skipped overly long keyword: ${keyword}`, 'warn');
                        }
                    } catch (tagError) {
                        log(`${progressPrefix} Error adding tag "${keyword}": ${tagError.message}`, 'warn');
                    }
                });

                if (addedKeywords > 0) {
                    modified = true;
                    log(`${progressPrefix} Added ${addedKeywords} keywords to: ${itemTitle}`);
                }
            }

            if (modified) {
                await item.save();
            }
        });

        return {
            success: true,
            reason: "Processed successfully",
            documentType,
            strategy: strategy.name
        };

    } catch (error) {
        log(`${progressPrefix} Error processing ${itemTitle}: ${error.message}`, 'error');
        return {
            success: false,
            reason: `Error: ${error.message}`,
            documentType: 'unknown',
            strategy: 'unknown'
        };
    }
}

// --- UTILITY FUNCTIONS FOR EXTERNAL ACCESS ---
function showAnalyticsReport() {
    if (!analytics) {
        return "Analytics not enabled. Set config.enableAnalytics = true";
    }

    const report = analytics.exportReport();
    console.log("Performance Analytics Report:", JSON.stringify(report, null, 2));
    return report;
}

function rollbackChanges(changeId = null) {
    if (!changeTracker) {
        return "Undo functionality not enabled. Set config.enableUndo = true";
    }

    return changeTracker.rollback(changeId);
}

function getChangesSummary() {
    if (!changeTracker) {
        return "Undo functionality not enabled. Set config.enableUndo = true";
    }

    return changeTracker.getChangesSummary();
}

function getCircuitBreakerStatus() {
    if (!circuitBreaker) {
        return "Circuit breaker not enabled. Set config.enableCircuitBreaker = true";
    }

    return circuitBreaker.getStatus();
}

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        config,
        configProfiles,
        DocumentTypeDetector,
        ExtractionStrategyFactory,
        AcademicPaperStrategy,
        ConferencePaperStrategy,
        TechnicalReportStrategy,
        GenericStrategy,
        ChangeTracker,
        CircuitBreaker,
        PerformanceAnalytics,
        PDFCache,
        RetryManager,
        ProgressTracker,
        TextPreprocessor,
        TestFramework,
        showAnalyticsReport,
        rollbackChanges,
        getChangesSummary,
        getCircuitBreakerStatus
    };
}






