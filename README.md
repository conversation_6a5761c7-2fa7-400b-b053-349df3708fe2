# Response Strength Analysis in R

## Overview

This R implementation provides tools to analyze behavioral data using the quantitative framework developed by <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (2002) in their paper "Molecular Analyses of the Principal Components of Response Strength." The framework integrates three key behavioral measures—response probability, latency, and rate—through mathematical models called "probability machines."

## Background

The original research demonstrated that response probability, latency, and response rate are highly correlated measures that can be unified under a common factor called "strength." This implementation allows researchers to:

- Analyze the underlying mechanisms that generate these correlations
- Apply precise mathematical models to behavioral data
- Compare different theoretical accounts of operant behavior
- Extract key parameters that characterize response strength

## Installation

### Required R Packages

```r
install.packages(c(
  "tidyverse",      # Data manipulation and visualization
  "markovchain",    # Markov model analysis
  "fitdistrplus",   # Distribution fitting
  "survival",       # Time-to-event analysis
  "MASS"           # Robust statistical functions
))
```

### Loading the Framework

```r
# Source the main analysis file
source("response_strength_analysis.R")
```

## Data Requirements

Your data should be in a data frame with the following structure:

### Required Columns
- `subject`: Subject identifier
- `session`: Session identifier  
- `trial`: Trial number within session

### Optional Columns (will be calculated if not provided)
- `response`: Binary response indicator (0/1)
- `responses`: Number of responses in trial
- `latency`: Time to first response
- `trial_duration`: Duration of each trial
- `response_times`: Comma-separated response timestamps
- `irt`: Inter-response times

### Example Data Structure

```r
# Example data format
example_data <- data.frame(
  subject = rep(1:4, each = 100),
  session = rep(1, 400),
  trial = rep(1:100, 4),
  responses = rpois(400, 2),
  latency = rexp(400, 1/0.5),
  trial_duration = rep(10, 400),
  response_times = "1.2,2.1,3.4"  # Example timestamps
)
```

## Usage

### Basic Analysis

```r
# 1. Load your data
data <- read.csv("your_behavioral_data.csv")

# 2. Run the complete analysis
results <- analyze_response_strength(data)

# 3. View results structure
str(results)
```

### Accessing Results

```r
# Get results for a specific subject
subject_1 <- results$by_subject$subject_1

# Two-state Markov model results
markov_results <- subject_1$markov
print(paste("P(respond|responded):", markov_results$p_1_given_1))
print(paste("P(respond|didn't respond):", markov_results$p_1_given_0))

# Latency distribution analysis
latency_results <- subject_1$latency
print(paste("Best latency model:", latency_results$best_model))

# IRT distribution analysis  
irt_results <- subject_1$irt
print(paste("IRT model type:", irt_results$model_type))

# Rate-probability relationship
rate_prob_results <- subject_1$rate_probability
print(paste("Best rate-prob model:", rate_prob_results$best_model))
```

### Aggregate Results

```r
# View aggregate statistics across all subjects
aggregate <- results$aggregate

# Average Markov model parameters
print("Average transition probabilities:")
print(aggregate$markov$mean_p_1_given_1)
print(aggregate$markov$mean_p_1_given_0)

# Model type distributions
print("Distribution of best-fitting models:")
print(aggregate$model_counts)
```

## Analysis Components

### 1. Two-State Markov Model

Analyzes whether animals alternate between "responsive" and "non-responsive" states across trials.

**Key Parameters:**
- `p_1_given_1`: Probability of responding given previous response
- `p_1_given_0`: Probability of responding given previous non-response
- `base_prob`: Overall probability of responding

**Interpretation:** Values around p(1|1) ≈ 0.96 and p(1|0) ≈ 0.37 suggest strong state-dependent responding, consistent with Killeen et al.'s findings.

### 2. Latency Distribution Analysis

Fits gamma/Erlang and extreme value (Gumbel) distributions to response latencies.

**Models Tested:**
- **Gamma Distribution**: Represents sequential processing stages
- **Erlang Distribution**: Special case of gamma with integer shape parameter
- **Gumbel Distribution**: Represents parallel processing with "slowest wins"

**Key Parameters:**
- `n_parameter`: Number of processing stages (Erlang)
- `tau_parameter`: Time parameter
- `mu`, `sigma`: Location and scale (Gumbel)

### 3. IRT Distribution Analysis

Analyzes inter-response time patterns to identify underlying timing mechanisms.

**Models Tested:**
- **Exponential**: Simple Poisson process
- **Shifted Exponential**: Refractory Poisson process
- **Palya-like**: Detects periodic patterns in responding

**Key Parameters:**
- `lambda`: Instantaneous response rate
- `delta`: Refractory period
- `periodicity_detected`: Whether periodic patterns were found

### 4. Rate-Probability Mapping

Tests the mathematical relationship between response rate and probability.

**Models Tested:**
- **Linear**: p ≈ Δb
- **Exponential**: p ≈ 1-e^(-λΔ)
- **Refractory Poisson**: p ≈ 1-e^(-(b/(1-δb))Δ)

**Key Finding:** At low rates, probability is proportional to rate. At higher rates, the relationship becomes curvilinear due to physical constraints.

## Interpreting Results

### Response Strength Assessment

High response strength is characterized by:
- High probability of continuing to respond after responding (p(1|1) > 0.9)
- Short, consistent latencies (low-variance gamma/Erlang distribution)
- High response rates with short refractory periods
- Strong rate-probability correlation

### Model Comparison

The framework automatically selects the best-fitting model for each component using AIC. Common patterns:

**Strong Operant Behavior:**
- Markov: High p(1|1), low p(1|0)
- Latency: Erlang distribution with low n
- IRT: Refractory Poisson with short δ
- Rate-Prob: Exponential or refractory model

**Weak/Variable Behavior:**
- Markov: p(1|1) ≈ p(1|0) ≈ base rate
- Latency: High-variance gamma or Gumbel
- IRT: Simple exponential
- Rate-Prob: Linear relationship

## Troubleshooting

### Common Issues

1. **"Insufficient data" warnings**: Ensure you have enough trials per subject (minimum 50-100 recommended)

2. **Model fitting failures**: Check for:
   - Negative or infinite values in your data
   - Trials with zero duration
   - Missing response time data

3. **Poor model fits**: May indicate:
   - Non-stationary behavior within sessions
   - Multiple behavioral modes not captured by current models
   - Data quality issues

### Data Quality Checks

```r
# Check data quality before analysis
check_data_quality <- function(data) {
  # Check for required columns
  required <- c("subject", "session", "trial")
  missing <- required[!required %in% colnames(data)]
  if(length(missing) > 0) {
    warning("Missing required columns: ", paste(missing, collapse = ", "))
  }
  
  # Check for reasonable values
  if("latency" %in% colnames(data)) {
    weird_latencies <- sum(data$latency < 0 | data$latency > 60, na.rm = TRUE)
    if(weird_latencies > 0) {
      warning(weird_latencies, " trials with unusual latencies (< 0 or > 60s)")
    }
  }
  
  # Check trial counts per subject
  trial_counts <- table(data$subject)
  low_count_subjects <- sum(trial_counts < 50)
  if(low_count_subjects > 0) {
    warning(low_count_subjects, " subjects with < 50 trials")
  }
}

# Run before main analysis
check_data_quality(your_data)
```

## Example Output Interpretation

```r
# Example interpretation for a typical subject
subject_results <- results$by_subject$subject_1

cat("Subject 1 Analysis Summary:\n")
cat("=========================\n")
cat("Markov Model:\n")
cat("  P(respond|responded) =", round(subject_results$markov$p_1_given_1, 3), "\n")
cat("  P(respond|no response) =", round(subject_results$markov$p_1_given_0, 3), "\n")
cat("  → Strong state dependence suggests high response strength\n\n")

cat("Latency Distribution:\n")
cat("  Best model:", subject_results$latency$best_model, "\n")
if(subject_results$latency$is_erlang) {
  cat("  Erlang parameters: n =", subject_results$latency$n_parameter, 
      ", τ =", round(subject_results$latency$tau_parameter, 3), "\n")
  cat("  → Consistent with", subject_results$latency$n_parameter, "processing stages\n\n")
}

cat("IRT Distribution:\n")
cat("  Model type:", subject_results$irt$model_type, "\n")
if(!is.null(subject_results$irt$delta)) {
  cat("  Refractory period:", round(subject_results$irt$delta, 3), "seconds\n")
}
cat("  → Indicates underlying timing mechanism\n\n")

cat("Rate-Probability Mapping:\n")
cat("  Best model:", subject_results$rate_probability$best_model, "\n")
cat("  → Describes how rate translates to probability\n")
```

## References

Killeen, P. R., Hall, S. S., Reilly, M. P., & Kettle, L. C. (2002). Molecular analyses of the principal components of response strength. *Journal of the Experimental Analysis of Behavior*, 78, 127-160.

## Citation

If you use this implementation in your research, please cite both the original paper and this implementation:

```
Killeen, P. R., Hall, S. S., Reilly, M. P., & Kettle, L. C. (2002). 
Molecular analyses of the principal components of response strength. 
Journal of the Experimental Analysis of Behavior, 78, 127-160.

[Your citation for this R implementation]
```

## License

This implementation is provided under the MIT License. See LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests to improve the implementation.

## Contact

For questions about this implementation, please [contact information].

For questions about the theoretical framework, refer to the original paper by Killeen et al. (2002).