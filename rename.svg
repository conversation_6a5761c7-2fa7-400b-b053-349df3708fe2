<svg viewBox="0 0 200 120" width="400" height="240" xmlns="http://www.w3.org/2000/svg">

  <defs>
    <!-- Optional: Define markers for arrowheads if desired -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <style>
    .path-line {
      stroke: black;
      stroke-width: 2;
      fill: none;
      /* marker-end: url(#arrowhead); */ /* Uncomment to add arrowheads */
    }
    .label-text {
      font-family: sans-serif;
      font-size: 10px;
      text-anchor: middle;
      fill: black;
    }
    .endpoint-text {
      font-family: sans-serif;
      font-size: 12px;
      font-weight: bold;
      text-anchor: middle;
      fill: #333;
    }
     .endpoint-desc {
      font-family: sans-serif;
      font-size: 9px;
      text-anchor: middle;
      fill: #555;
    }
  </style>

  <!-- Starting Path -->
  <path d="M 100 110 V 70" class="path-line" />

  <!-- Split Paths -->
  <!-- Path to Dominance (Left) -->
  <path d="M 100 70 Q 80 60, 50 30" class="path-line" />
  <!-- Path to Prestige (Right) -->
  <path d="M 100 70 Q 120 60, 150 30" class="path-line" />

  <!-- Labels -->
  <text x="100" y="118" class="label-text">Start / Status</text>

  <!-- Endpoints -->
  <!-- Dominance Endpoint -->
  <text x="50" y="15" class="endpoint-text">Dominance</text>
  <text x="50" y="25" class="endpoint-desc">(Force)</text>

  <!-- Prestige Endpoint -->
  <text x="150" y="15" class="endpoint-text">Prestige</text>
   <text x="150" y="25" class="endpoint-desc">(Skill / Help)</text>

</svg>