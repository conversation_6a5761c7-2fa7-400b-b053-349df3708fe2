/**
 * Advanced Abstract and Keyword Extractor for Zotero
 *
 * This script processes selected Zotero items to find their best PDF attachment,
 * then extracts and cleans the abstract and keywords from the PDF's text content.
 *
 * Features:
 * - Configurable labels for 'Abstract' and 'Keywords' (supports multiple languages).
 * - Option to prevent overwriting existing abstracts.
 * - Option to add a prefix to all created tags.
 * - Extracts abstract and keywords independently.
 * - Advanced text cleaning: de-hyphenates words, normalizes whitespace.
 * - Normalizes keywords: converts to lowercase and removes duplicates.
 * - User safety: Asks for confirmation before running.
 * - Robust error handling and detailed progress logging.
 * - Uses atomic database transactions for data integrity.
 *
 * <AUTHOR> with major enhancements by Gemini & the Zotero community
 * @link https://github.com/windingwind/zotero-actions-tags/discussions/136
 */

// --- CONFIGURATION ---
const config = {
	// Set to false to prevent the script from overwriting an abstract that already exists.
	overwriteAbstract: true,
	// Add a prefix to all keywords extracted by this script.
	// Example: "auto:" results in tags like "auto:machine learning". Use "" for no prefix.
	tagPrefix: "",
	// Alternative names for the 'Abstract' section (case-insensitive). Add more as needed.
	abstractLabels: ["Abstract", "Summary"],
	// Alternative names for the 'Keywords' section (case-insensitive). Add more as needed.
	keywordLabels: ["Keywords", "Key words", "Index Terms"],
};
// --- END CONFIGURATION ---

// --- SCRIPT INITIALIZATION ---

// Get access to Zotero services and the main window pane
const { Zotero } = Components.classes['@zotero.org/Zotero;1'].getService(Components.interfaces.nsISupports);
const ZoteroPane = Zotero.getActiveZoteroPane();

// Helper function for clear logging in the Zotero debug console
function log(message) {
	Zotero.debug(`[Adv. Abstract Extractor] ${message}`);
}

// Dynamically build regular expressions from the configuration
const abstractRegexPart = config.abstractLabels.join('|');
const keywordRegexPart = config.keywordLabels.join('|');

// Regex to find the abstract. It stops capturing if it sees a keyword section or common introduction headings.
const abstractRegex = new RegExp(`\\n\\s*(?:${abstractRegexPart})\\s*[:\\-\\s\\n]([\\s\\S]+?)(?=\\n\\s*(?:${keywordRegexPart}|1\\.?\\s+Introduction|I\\.\\s+INTRODUCTION))`, 'i');

// Regex to find the line with keywords. It captures everything to the end of the line.
const keywordRegex = new RegExp(`\\n\\s*(?:${keywordRegexPart})\\s*[:\\-\\s\\n](.+)`, 'i');


// --- MAIN EXECUTION ---

// Get the currently selected items in the Zotero library
const items = ZoteroPane.getSelectedItems();

// Check if any items are selected
if (!items.length) {
	return "No items selected.";
}

// Safety first: Confirm with the user before proceeding with modifications
const promptService = Components.classes["@mozilla.org/embedcomp/prompt-service;1"].getService(Components.interfaces.nsIPromptService);
if (!promptService.confirm(null, "Advanced Abstract Extractor", `This will process ${items.length} item(s) to find and add abstracts and keywords. This action modifies your library. Continue?`)) {
	return "Operation cancelled by user.";
}

let processedCount = 0;
log(`Starting operation for ${items.length} items.`);

// Process all selected items concurrently
await Promise.all(items.map(async (item, index) => {
	const progressPrefix = `[${index + 1}/${items.length}]`;
	const itemTitle = item.getDisplayTitle();

	// 1. Validate the item
	if (!item.isRegularItem()) {
		log(`${progressPrefix} Skipping non-regular item: ${itemTitle}`);
		return;
	}

	const attachment = await item.getBestAttachment();
	if (!attachment || attachment.attachmentContentType !== 'application/pdf') {
		log(`${progressPrefix} No usable PDF attachment found for: ${itemTitle}`);
		return;
	}

	// 2. Extract and Process PDF Text
	try {
		const pdfText = (await Zotero.PDFWorker.getFullText(attachment.id, 2)).text;
		let foundAbstract = "";
		let foundKeywords = [];

		// Attempt to extract abstract
		const abstractMatch = pdfText.match(abstractRegex);
		if (abstractMatch && abstractMatch[1]) {
			foundAbstract = abstractMatch[1]
				.replace(/-\n\s*/g, '')    // De-hyphenate words broken across lines
				.replace(/\s+/g, ' ')      // Condense all whitespace to single spaces
				.trim();
		}

		// Attempt to extract keywords
		const keywordMatch = pdfText.match(keywordRegex);
		if (keywordMatch && keywordMatch[1]) {
			foundKeywords = keywordMatch[1]
				.trim()
				.split(/[;,.]\s*/) // Split by semicolon, comma, or period
				.map(k => k.trim().toLowerCase()) // Normalize to lowercase
				.filter((k, idx, self) => k && self.indexOf(k) === idx); // Filter out empty strings and duplicates
		}

		// 3. Update Zotero Item if new data was found
		if (foundAbstract || foundKeywords.length > 0) {
			await Zotero.DB.executeTransaction(async () => {
				let modified = false;

				// Update abstract if found and allowed
				if (foundAbstract && (config.overwriteAbstract || !item.getField("abstractNote"))) {
					item.setField("abstractNote", foundAbstract);
					modified = true;
				}

				// Add new tags
				if (foundKeywords.length > 0) {
					// Zotero handles checking for existing tags automatically
					foundKeywords.forEach(keyword => item.addTag(config.tagPrefix + keyword));
					modified = true;
				}
				
				// Only save if changes were actually made
				if (modified) {
					await item.save();
				}
			});
			processedCount++;
			log(`${progressPrefix} Successfully processed: ${itemTitle}`);
		} else {
			log(`${progressPrefix} Could not find abstract or keywords for: ${itemTitle}`);
		}

	} catch (error) {
		Zotero.logError(`[Adv. Abstract Extractor] ${progressPrefix} Error processing item ${item.id} (${itemTitle}): ${error}`);
	}
}));

// --- FINAL REPORT ---
log(`Operation complete. Successfully processed ${processedCount} of ${items.length} items.`);
return `Processed ${processedCount} / ${items.length} items. Check debug log for details.`;