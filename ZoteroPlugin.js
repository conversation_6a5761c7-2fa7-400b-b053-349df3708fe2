/**
 * Advanced Abstract and Keyword Extractor for Zotero
 *
 * This script processes selected Zotero items to find their best PDF attachment,
 * then extracts and cleans the abstract and keywords from the PDF's text content.
 *
 * Features:
 * - Configurable labels for 'Abstract' and 'Keywords' (supports multiple languages).
 * - Option to prevent overwriting existing abstracts.
 * - Option to add a prefix to all created tags.
 * - Extracts abstract and keywords independently.
 * - Advanced text cleaning: de-hyphenates words, normalizes whitespace.
 * - Normalizes keywords: converts to lowercase and removes duplicates.
 * - User safety: Asks for confirmation before running.
 * - Robust error handling and detailed progress logging.
 * - Uses atomic database transactions for data integrity.
 * - Rate limiting to prevent system overload.
 * - Enhanced security against ReDoS attacks.
 *
 * <AUTHOR> with major enhancements by Gemini & the Zotero community
 * @link https://github.com/windingwind/zotero-actions-tags/discussions/136
 * @version 2.0.0
 */

// --- CONFIGURATION ---
const config = {
	// Set to false to prevent the script from overwriting an abstract that already exists.
	overwriteAbstract: true,

	// Add a prefix to all keywords extracted by this script.
	// Example: "auto:" results in tags like "auto:machine learning". Use "" for no prefix.
	// Maximum length: 50 characters to prevent database issues
	tagPrefix: "",

	// Alternative names for the 'Abstract' section (case-insensitive). Add more as needed.
	// Supports multiple languages: English, German, French, Spanish, etc.
	abstractLabels: ["Abstract", "Summary", "Zusammenfassung", "Résumé", "Resumen"],

	// Alternative names for the 'Keywords' section (case-insensitive). Add more as needed.
	// Supports multiple languages and common variations
	keywordLabels: ["Keywords", "Key words", "Index Terms", "Schlüsselwörter", "Mots-clés", "Palabras clave"],

	// Maximum number of items to process concurrently (prevents system overload)
	maxConcurrentItems: 5,

	// Maximum text length to process (prevents memory issues with very large PDFs)
	maxTextLength: 1000000, // 1MB of text

	// Timeout for PDF text extraction (milliseconds)
	pdfExtractionTimeout: 30000, // 30 seconds
};

// Validate configuration
if (config.tagPrefix.length > 50) {
	throw new Error("tagPrefix must be 50 characters or less");
}
if (config.maxConcurrentItems < 1 || config.maxConcurrentItems > 20) {
	throw new Error("maxConcurrentItems must be between 1 and 20");
}
// --- END CONFIGURATION ---

// --- SCRIPT INITIALIZATION ---

// Get access to Zotero services
// Support both legacy and modern Zotero API access patterns
let Zotero;

// Modern Zotero API initialization (Zotero 6+)
if (typeof window !== 'undefined' && window.Zotero) {
	Zotero = window.Zotero;
}
// Global Zotero object (common in script contexts)
else if (typeof globalThis !== 'undefined' && globalThis.Zotero) {
	Zotero = globalThis.Zotero;
}
// Check if Zotero is available as a global variable
else if (typeof Zotero !== 'undefined') {
	// Zotero is already available globally
	// This is the most common case for Zotero scripts
}
// Legacy XPCOM approach (Zotero 5 and earlier) - only if Components is available
else if (typeof Components !== 'undefined' && Components.classes) {
	try {
		Zotero = Components.classes['@zotero.org/Zotero;1']
			.getService(Components.interfaces.nsISupports).wrappedJSObject;
	} catch (error) {
		throw new Error(`Legacy Zotero API initialization failed: ${error.message}`);
	}
}
else {
	throw new Error("Could not find Zotero API. Please ensure this script is run within Zotero.");
}

if (!Zotero) {
	throw new Error("Zotero API is not available. Please ensure Zotero is running and this script is executed in the correct context.");
}

/**
 * Enhanced logging function with timestamp and error levels
 * @param {string} message - The message to log
 * @param {string} level - Log level: 'info', 'warn', 'error'
 */
function log(message, level = 'info') {
	const timestamp = new Date().toISOString();
	const prefix = `[Adv. Abstract Extractor ${timestamp}]`;

	switch (level) {
		case 'error':
			Zotero.logError(`${prefix} ERROR: ${message}`);
			break;
		case 'warn':
			Zotero.debug(`${prefix} WARNING: ${message}`, 2);
			break;
		default:
			Zotero.debug(`${prefix} ${message}`);
	}
}

/**
 * Escapes special regex characters to prevent ReDoS attacks
 * @param {string} string - String to escape
 * @returns {string} Escaped string safe for regex
 */
function escapeRegex(string) {
	return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Safely build regular expressions from the configuration with ReDoS protection
const abstractLabelsEscaped = config.abstractLabels.map(escapeRegex);
const keywordLabelsEscaped = config.keywordLabels.map(escapeRegex);

const abstractRegexPattern = abstractLabelsEscaped.join('|');
const keywordRegexPattern = keywordLabelsEscaped.join('|');

// More conservative regex patterns to prevent ReDoS attacks
// Limited capture groups and non-greedy matching with reasonable bounds
const abstractRegex = new RegExp(
	`\\n\\s*(?:${abstractRegexPattern})\\s*[:\\-\\s\\n]([^\\n]{1,50}[\\s\\S]{0,2000}?)(?=\\n\\s*(?:${keywordRegexPattern}|1\\.?\\s+Introduction|I\\.\\s+INTRODUCTION|\\n\\s*[A-Z]))`,
	'i'
);

// Simpler, safer keyword regex with length limits
const keywordRegex = new RegExp(
	`\\n\\s*(?:${keywordRegexPattern})\\s*[:\\-\\s\\n]([^\\n]{1,500})`,
	'i'
);


// --- UTILITY FUNCTIONS ---

/**
 * Validates that an item is suitable for processing
 * @param {Object} item - Zotero item to validate
 * @returns {Object} Validation result with isValid boolean and reason string
 */
function validateItem(item) {
	if (!item) {
		return { isValid: false, reason: "Item is null or undefined" };
	}

	if (!item.isRegularItem()) {
		return { isValid: false, reason: "Not a regular item (may be attachment, note, etc.)" };
	}

	return { isValid: true, reason: "" };
}

/**
 * Safely extracts PDF text with timeout and error handling
 * @param {number} attachmentId - ID of the PDF attachment
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<string>} Extracted text or empty string on failure
 */
async function extractPdfTextSafely(attachmentId, timeout = config.pdfExtractionTimeout) {
	return new Promise(async (resolve) => {
		const timeoutId = setTimeout(() => {
			log(`PDF text extraction timed out for attachment ${attachmentId}`, 'warn');
			resolve("");
		}, timeout);

		try {
			// The second parameter (2) requests full text extraction with OCR fallback
			const result = await Zotero.PDFWorker.getFullText(attachmentId, 2);
			clearTimeout(timeoutId);

			if (!result || typeof result.text !== 'string') {
				log(`PDF text extraction returned invalid result for attachment ${attachmentId}`, 'warn');
				resolve("");
				return;
			}

			// Limit text length to prevent memory issues
			const text = result.text.length > config.maxTextLength
				? result.text.substring(0, config.maxTextLength)
				: result.text;

			resolve(text);
		} catch (error) {
			clearTimeout(timeoutId);
			log(`Error extracting PDF text for attachment ${attachmentId}: ${error.message}`, 'error');
			resolve("");
		}
	});
}

/**
 * Processes text to extract and clean abstract
 * @param {string} text - PDF text content
 * @returns {string} Cleaned abstract or empty string
 */
function extractAbstract(text) {
	if (!text || typeof text !== 'string') {
		return "";
	}

	try {
		const match = text.match(abstractRegex);
		if (!match || !match[1]) {
			return "";
		}

		return match[1]
			.replace(/-\n\s*/g, '')    // De-hyphenate words broken across lines
			.replace(/\s+/g, ' ')      // Condense all whitespace to single spaces
			.replace(/[^\w\s.,;:!?()-]/g, '') // Remove unusual characters that might cause issues
			.trim()
			.substring(0, 10000); // Limit abstract length
	} catch (error) {
		log(`Error extracting abstract: ${error.message}`, 'error');
		return "";
	}
}

/**
 * Processes text to extract and normalize keywords
 * @param {string} text - PDF text content
 * @returns {Array<string>} Array of normalized keywords
 */
function extractKeywords(text) {
	if (!text || typeof text !== 'string') {
		return [];
	}

	try {
		const match = text.match(keywordRegex);
		if (!match || !match[1]) {
			return [];
		}

		return match[1]
			.trim()
			.split(/[;,.]\s*/) // Split by semicolon, comma, or period
			.map(k => k.trim().toLowerCase()) // Normalize to lowercase
			.filter(k => k && k.length >= 2 && k.length <= 100) // Filter reasonable keywords
			.filter((k, idx, self) => self.indexOf(k) === idx) // Remove duplicates
			.slice(0, 50); // Limit number of keywords
	} catch (error) {
		log(`Error extracting keywords: ${error.message}`, 'error');
		return [];
	}
}

// --- MAIN EXECUTION ---

// Use the items variable passed to the script (recommended approach for Zotero scripts)
// This variable should be available in the script context when run from Zotero
if (typeof items === 'undefined') {
	return "No items available. This script should be run from Zotero with selected items.";
}

// Check if any items are selected
if (!items || !Array.isArray(items) || !items.length) {
	return "No items selected. Please select items in Zotero before running this script.";
}

// Validate selection size
if (items.length > 100) {
	return "Too many items selected (maximum 100). Please select fewer items to prevent system overload.";
}

// Safety first: Confirm with the user before proceeding with modifications
let promptService;
try {
	// Try modern approach first
	if (typeof Services !== 'undefined' && Services.prompt) {
		promptService = Services.prompt;
	}
	// Fallback to Components if available
	else if (typeof Components !== 'undefined' && Components.classes) {
		promptService = Components.classes["@mozilla.org/embedcomp/prompt-service;1"]
			.getService(Components.interfaces.nsIPromptService);
	}
	else {
		throw new Error("Neither Services.prompt nor Components.classes is available");
	}
} catch (error) {
	log(`Could not access prompt service: ${error.message}`, 'error');
	return "Could not display confirmation dialog. Operation cancelled for safety.";
}

const confirmMessage = `This will process ${items.length} item(s) to find and add abstracts and keywords.\n\n` +
	`Configuration:\n` +
	`- Overwrite existing abstracts: ${config.overwriteAbstract ? 'Yes' : 'No'}\n` +
	`- Tag prefix: "${config.tagPrefix}"\n` +
	`- Max concurrent processing: ${config.maxConcurrentItems}\n\n` +
	`This action modifies your library. Continue?`;

if (!promptService.confirm(null, "Advanced Abstract Extractor v2.0", confirmMessage)) {
	return "Operation cancelled by user.";
}

let processedCount = 0;
let errorCount = 0;
log(`Starting operation for ${items.length} items with enhanced safety features.`);

// Process items with controlled concurrency to prevent system overload
const processItem = async (item, index) => {
	const progressPrefix = `[${index + 1}/${items.length}]`;
	let itemTitle = "Unknown";

	try {
		itemTitle = item.getDisplayTitle() || `Item ${item.id}`;

		// 1. Validate the item
		const validation = validateItem(item);
		if (!validation.isValid) {
			log(`${progressPrefix} Skipping item: ${itemTitle} - ${validation.reason}`);
			return { success: false, reason: validation.reason };
		}

		// 2. Find and validate PDF attachment
		const attachment = await item.getBestAttachment();
		if (!attachment) {
			log(`${progressPrefix} No attachment found for: ${itemTitle}`);
			return { success: false, reason: "No attachment found" };
		}

		// Safely check attachment content type
		const contentType = attachment.attachmentContentType || attachment.contentType || "";
		if (contentType !== 'application/pdf') {
			log(`${progressPrefix} No PDF attachment found for: ${itemTitle} (found: ${contentType})`);
			return { success: false, reason: "No PDF attachment" };
		}

		// 3. Extract PDF text with safety measures
		const pdfText = await extractPdfTextSafely(attachment.id);
		if (!pdfText) {
			log(`${progressPrefix} Could not extract text from PDF for: ${itemTitle}`);
			return { success: false, reason: "PDF text extraction failed" };
		}

		// 4. Extract abstract and keywords using safe functions
		const foundAbstract = extractAbstract(pdfText);
		const foundKeywords = extractKeywords(pdfText);

		// 5. Update Zotero Item if new data was found
		if (foundAbstract || foundKeywords.length > 0) {
			try {
				await Zotero.DB.executeTransaction(async () => {
					let modified = false;

					// Update abstract if found and allowed
					if (foundAbstract) {
						const existingAbstract = item.getField("abstractNote");
						if (config.overwriteAbstract || !existingAbstract) {
							item.setField("abstractNote", foundAbstract);
							modified = true;
							log(`${progressPrefix} Added abstract (${foundAbstract.length} chars) to: ${itemTitle}`);
						} else {
							log(`${progressPrefix} Skipped abstract (already exists) for: ${itemTitle}`);
						}
					}

					// Add new tags with validation
					if (foundKeywords.length > 0) {
						let addedKeywords = 0;
						foundKeywords.forEach(keyword => {
							try {
								const tagWithPrefix = config.tagPrefix + keyword;
								// Validate tag length (Zotero has limits)
								if (tagWithPrefix.length <= 255) {
									item.addTag(tagWithPrefix);
									addedKeywords++;
								} else {
									log(`${progressPrefix} Skipped overly long keyword: ${keyword}`, 'warn');
								}
							} catch (tagError) {
								log(`${progressPrefix} Error adding tag "${keyword}": ${tagError.message}`, 'warn');
							}
						});

						if (addedKeywords > 0) {
							modified = true;
							log(`${progressPrefix} Added ${addedKeywords} keywords to: ${itemTitle}`);
						}
					}

					// Only save if changes were actually made
					if (modified) {
						await item.save();
					}
				});

				log(`${progressPrefix} Successfully processed: ${itemTitle}`);
				return { success: true, reason: "Processed successfully" };
			} catch (dbError) {
				log(`${progressPrefix} Database error for ${itemTitle}: ${dbError.message}`, 'error');
				return { success: false, reason: `Database error: ${dbError.message}` };
			}
		} else {
			log(`${progressPrefix} No abstract or keywords found for: ${itemTitle}`);
			return { success: false, reason: "No extractable content found" };
		}

	} catch (error) {
		log(`${progressPrefix} Unexpected error processing ${itemTitle}: ${error.message}`, 'error');
		return { success: false, reason: `Unexpected error: ${error.message}` };
	}
};

// Process items in batches to control concurrency
const results = [];
for (let i = 0; i < items.length; i += config.maxConcurrentItems) {
	const batch = items.slice(i, i + config.maxConcurrentItems);
	const batchPromises = batch.map((item, batchIndex) =>
		processItem(item, i + batchIndex)
	);

	const batchResults = await Promise.all(batchPromises);
	results.push(...batchResults);

	// Brief pause between batches to prevent overwhelming the system
	if (i + config.maxConcurrentItems < items.length) {
		await new Promise(resolve => setTimeout(resolve, 100));
	}
}

// Count results
processedCount = results.filter(r => r.success).length;
errorCount = results.filter(r => !r.success).length;

// --- FINAL REPORT ---
const successRate = items.length > 0 ? Math.round((processedCount / items.length) * 100) : 0;
log(`Operation complete. Successfully processed ${processedCount} of ${items.length} items (${successRate}% success rate). ${errorCount} items had errors.`);

// Provide detailed summary
let summaryMessage = `Abstract & Keyword Extraction Complete\n\n`;
summaryMessage += `Results:\n`;
summaryMessage += `✓ Successfully processed: ${processedCount} items\n`;
summaryMessage += `✗ Failed/Skipped: ${errorCount} items\n`;
summaryMessage += `📊 Success rate: ${successRate}%\n\n`;

if (errorCount > 0) {
	summaryMessage += `Common reasons for failures:\n`;
	summaryMessage += `• No PDF attachments found\n`;
	summaryMessage += `• PDF text extraction failed\n`;
	summaryMessage += `• No recognizable abstract/keywords in PDF\n\n`;
}

summaryMessage += `Check the Zotero debug log (Help → Debug Output Logging) for detailed information.`;

// Show summary dialog to user
try {
	promptService.alert(null, "Abstract Extractor - Results", summaryMessage);
} catch (error) {
	log(`Could not display results dialog: ${error.message}`, 'warn');
}

return `Processed ${processedCount}/${items.length} items (${successRate}% success). Check debug log for details.`;